<?php
/**
 * Database Structure Inspector
 * Check what tables and columns actually exist before making changes
 */

require_once 'config/config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Database Structure Inspector</h1>";
    echo "<p>Database: " . DB_NAME . "</p>";
    
    // Get all tables
    echo "<h2>All Tables in Database</h2>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<ul>";
    foreach ($tables as $table) {
        echo "<li>" . htmlspecialchars($table) . "</li>";
    }
    echo "</ul>";
    
    // Check specific tables we care about
    $importantTables = ['images', 'event_photo_metadata', 'event_photo_custom_categories', 'users'];
    
    foreach ($importantTables as $tableName) {
        echo "<h2>Table: $tableName</h2>";
        
        try {
            $stmt = $pdo->query("DESCRIBE `$tableName`");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr style='background-color: #f0f0f0;'>";
            echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
            echo "</tr>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                echo "<td>" . htmlspecialchars($column['Default'] ?? 'NULL') . "</td>";
                echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show foreign keys for this table
            echo "<h3>Foreign Keys for $tableName</h3>";
            $stmt = $pdo->query("
                SELECT 
                    COLUMN_NAME,
                    CONSTRAINT_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = '" . DB_NAME . "' 
                AND TABLE_NAME = '$tableName' 
                AND REFERENCED_TABLE_NAME IS NOT NULL
            ");
            $foreignKeys = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($foreignKeys) > 0) {
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr style='background-color: #f0f0f0;'>";
                echo "<th>Column</th><th>Constraint</th><th>References Table</th><th>References Column</th>";
                echo "</tr>";
                
                foreach ($foreignKeys as $fk) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($fk['COLUMN_NAME']) . "</td>";
                    echo "<td>" . htmlspecialchars($fk['CONSTRAINT_NAME']) . "</td>";
                    echo "<td>" . htmlspecialchars($fk['REFERENCED_TABLE_NAME']) . "</td>";
                    echo "<td>" . htmlspecialchars($fk['REFERENCED_COLUMN_NAME']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No foreign keys found.</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Table '$tableName' does not exist or error: " . $e->getMessage() . "</p>";
        }
    }
    
    // Check if images table has the right structure for event photos
    echo "<h2>Images Table Analysis</h2>";
    try {
        $stmt = $pdo->query("SELECT DISTINCT entity_type FROM images LIMIT 10");
        $entityTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<p>Entity types found in images table: " . implode(', ', $entityTypes) . "</p>";
        
        // Check if event_photo entity type exists
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM images WHERE entity_type = 'event_photo'");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Event photos in images table: " . $result['count'] . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking images table: " . $e->getMessage() . "</p>";
    }
    
    // Check existing event photo metadata if table exists
    echo "<h2>Event Photo Metadata Analysis</h2>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM event_photo_metadata");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Records in event_photo_metadata: " . $result['count'] . "</p>";
        
        if ($result['count'] > 0) {
            $stmt = $pdo->query("SELECT DISTINCT category FROM event_photo_metadata");
            $categories = $stmt->fetchAll(PDO::FETCH_COLUMN);
            echo "<p>Categories in use: " . implode(', ', $categories) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>event_photo_metadata table does not exist yet: " . $e->getMessage() . "</p>";
    }
    
    // Check MySQL version and engine support
    echo "<h2>MySQL Information</h2>";
    $stmt = $pdo->query("SELECT VERSION() as version");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>MySQL Version: " . $result['version'] . "</p>";
    
    $stmt = $pdo->query("SHOW ENGINES");
    $engines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "<p>Available Storage Engines:</p>";
    echo "<ul>";
    foreach ($engines as $engine) {
        echo "<li>" . $engine['Engine'] . " - " . $engine['Support'] . "</li>";
    }
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h1 style='color: red;'>Database Connection Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "<p>Check your database configuration in config/config.php</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>
