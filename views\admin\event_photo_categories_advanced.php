<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-tags me-2"></i><?php echo $data['title']; ?></h2>
                    <p class="text-muted">Manage unlimited custom categories with emojis and icons for event photos</p>
                </div>
                <div class="btn-group">
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>Add Category
                    </button>
                    <a href="<?php echo BASE_URL; ?>/admin/settings_event_photos" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Event Photos
                    </a>
                </div>
            </div>

            <!-- Flash Messages -->
            <?php if (isset($_SESSION['flash_message'])): ?>
                <div class="alert alert-<?php echo $_SESSION['flash_message']['type']; ?> alert-dismissible fade show" role="alert">
                    <?php echo $_SESSION['flash_message']['message']; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                <?php unset($_SESSION['flash_message']); ?>
            <?php endif; ?>

            <!-- Statistics Card -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count($data['categories']); ?></h4>
                                    <p class="mb-0">Total Categories</p>
                                </div>
                                <i class="fas fa-tags fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo count(array_filter($data['categories'], function($c) { return $c->is_active; })); ?></h4>
                                    <p class="mb-0">Active Categories</p>
                                </div>
                                <i class="fas fa-check-circle fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $data['total_photos'] ?? 0; ?></h4>
                                    <p class="mb-0">Total Photos</p>
                                </div>
                                <i class="fas fa-camera fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h4><?php echo $data['most_used_category'] ?? 'N/A'; ?></h4>
                                    <p class="mb-0">Most Used</p>
                                </div>
                                <i class="fas fa-star fa-2x opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Management -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Manage Categories</h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" id="selectAll">Select All</button>
                        <button class="btn btn-outline-secondary" id="deselectAll">Deselect All</button>
                        <button class="btn btn-outline-success" id="enableSelected">Enable Selected</button>
                        <button class="btn btn-outline-warning" id="disableSelected">Disable Selected</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Tips:</strong> Drag categories to reorder them. Use the checkboxes for bulk actions. Click the edit button to modify individual categories.
                    </div>

                    <div id="categoriesList" class="sortable-list">
                        <?php if (empty($data['categories'])): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No categories found</h5>
                                <p class="text-muted">Click "Add Category" to create your first custom category.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($data['categories'] as $category): ?>
                                <div class="category-item mb-3" data-id="<?php echo $category->id; ?>" data-sort="<?php echo $category->sort_order; ?>">
                                    <div class="card <?php echo $category->is_active ? 'border-success' : 'border-secondary'; ?>">
                                        <div class="card-body p-3">
                                            <div class="d-flex align-items-center justify-content-between">
                                                <div class="d-flex align-items-center">
                                                    <input type="checkbox" class="form-check-input me-3 category-checkbox" 
                                                           value="<?php echo $category->id; ?>">
                                                    <i class="fas fa-grip-vertical text-muted me-3 drag-handle" style="cursor: move;"></i>
                                                    <div class="category-preview me-3">
                                                        <span class="badge bg-<?php echo $category->is_active ? 'primary' : 'secondary'; ?> fs-6">
                                                            <?php echo htmlspecialchars($category->emoji_icon); ?> 
                                                            <?php echo htmlspecialchars($category->category_label); ?>
                                                        </span>
                                                    </div>
                                                    <small class="text-muted">
                                                        Key: <code><?php echo htmlspecialchars($category->category_key); ?></code>
                                                        <?php if (isset($data['category_usage'][$category->category_key])): ?>
                                                            | Used: <?php echo $data['category_usage'][$category->category_key]; ?> photos
                                                        <?php endif; ?>
                                                    </small>
                                                </div>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-<?php echo $category->is_active ? 'warning' : 'success'; ?> toggle-status" 
                                                            data-id="<?php echo $category->id; ?>"
                                                            title="<?php echo $category->is_active ? 'Disable' : 'Enable'; ?> Category">
                                                        <i class="fas fa-<?php echo $category->is_active ? 'eye-slash' : 'eye'; ?>"></i>
                                                    </button>
                                                    <button class="btn btn-outline-primary edit-category" 
                                                            data-id="<?php echo $category->id; ?>"
                                                            data-key="<?php echo htmlspecialchars($category->category_key); ?>"
                                                            data-label="<?php echo htmlspecialchars($category->category_label); ?>"
                                                            data-emoji="<?php echo htmlspecialchars($category->emoji_icon); ?>"
                                                            title="Edit Category">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-outline-danger delete-category" 
                                                            data-id="<?php echo $category->id; ?>"
                                                            data-label="<?php echo htmlspecialchars($category->category_label); ?>"
                                                            title="Delete Category">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Category Modal -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus me-2"></i>Add New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCategoryForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                    
                    <div class="mb-3">
                        <label for="add_category_key" class="form-label">Category Key <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="add_category_key" name="category_key" 
                               placeholder="e.g., engine_bay" pattern="[a-z0-9_]+" required>
                        <div class="form-text">Lowercase letters, numbers, and underscores only. This cannot be changed later.</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_category_label" class="form-label">Category Label <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="add_category_label" name="category_label" 
                               placeholder="e.g., Engine Bay" maxlength="100" required>
                        <div class="form-text">Display name shown to users</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="add_emoji_icon" class="form-label">Emoji Icon <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="add_emoji_icon" name="emoji_icon" 
                                   placeholder="🔧" maxlength="10" required>
                            <button type="button" class="btn btn-outline-secondary" id="emojiPickerBtn">
                                <i class="fas fa-smile"></i> Pick
                            </button>
                        </div>
                        <div class="form-text">Choose an emoji or enter a custom icon</div>
                    </div>
                    
                    <!-- Comprehensive Emoji Picker -->
                    <div id="emojiPicker" class="mb-3" style="display: none;">
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">Choose an Emoji:</h6>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary emoji-category-btn active" data-category="cars">Cars</button>
                                    <button type="button" class="btn btn-outline-secondary emoji-category-btn" data-category="tools">Tools</button>
                                    <button type="button" class="btn btn-outline-secondary emoji-category-btn" data-category="awards">Awards</button>
                                    <button type="button" class="btn btn-outline-secondary emoji-category-btn" data-category="people">People</button>
                                    <button type="button" class="btn btn-outline-secondary emoji-category-btn" data-category="objects">Objects</button>
                                    <button type="button" class="btn btn-outline-secondary emoji-category-btn" data-category="symbols">Symbols</button>
                                </div>
                            </div>

                            <!-- Car Related Emojis -->
                            <div class="emoji-category" data-category="cars">
                                <div class="emoji-grid">
                                    <span class="emoji-option">🚗</span><span class="emoji-option">🏎️</span><span class="emoji-option">🚙</span><span class="emoji-option">🚕</span>
                                    <span class="emoji-option">🚐</span><span class="emoji-option">🛻</span><span class="emoji-option">🚚</span><span class="emoji-option">🚛</span>
                                    <span class="emoji-option">🏍️</span><span class="emoji-option">🛵</span><span class="emoji-option">🚲</span><span class="emoji-option">🛴</span>
                                    <span class="emoji-option">🚓</span><span class="emoji-option">🚑</span><span class="emoji-option">🚒</span><span class="emoji-option">🚌</span>
                                    <span class="emoji-option">🚜</span><span class="emoji-option">🏁</span><span class="emoji-option">🛞</span><span class="emoji-option">⛽</span>
                                </div>
                            </div>

                            <!-- Tools & Mechanical -->
                            <div class="emoji-category" data-category="tools" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">🔧</span><span class="emoji-option">🔨</span><span class="emoji-option">⚙️</span><span class="emoji-option">🛠️</span>
                                    <span class="emoji-option">🪛</span><span class="emoji-option">🔩</span><span class="emoji-option">⚡</span><span class="emoji-option">🔋</span>
                                    <span class="emoji-option">🪫</span><span class="emoji-option">💡</span><span class="emoji-option">🔌</span><span class="emoji-option">💨</span>
                                    <span class="emoji-option">🔥</span><span class="emoji-option">💧</span><span class="emoji-option">🛢️</span><span class="emoji-option">⛽</span>
                                </div>
                            </div>

                            <!-- Awards & Competition -->
                            <div class="emoji-category" data-category="awards" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">🏆</span><span class="emoji-option">🥇</span><span class="emoji-option">🥈</span><span class="emoji-option">🥉</span>
                                    <span class="emoji-option">🏅</span><span class="emoji-option">🎖️</span><span class="emoji-option">👑</span><span class="emoji-option">⭐</span>
                                    <span class="emoji-option">🌟</span><span class="emoji-option">✨</span><span class="emoji-option">💎</span><span class="emoji-option">🎯</span>
                                    <span class="emoji-option">🎪</span><span class="emoji-option">🎭</span><span class="emoji-option">🎨</span><span class="emoji-option">🎬</span>
                                </div>
                            </div>

                            <!-- People & Social -->
                            <div class="emoji-category" data-category="people" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">👥</span><span class="emoji-option">👤</span><span class="emoji-option">👨‍⚖️</span><span class="emoji-option">👨‍🔧</span>
                                    <span class="emoji-option">👩‍🔧</span><span class="emoji-option">👨‍💼</span><span class="emoji-option">👩‍💼</span><span class="emoji-option">👨‍🏭</span>
                                    <span class="emoji-option">👩‍🏭</span><span class="emoji-option">🧑‍🤝‍🧑</span><span class="emoji-option">👫</span><span class="emoji-option">👬</span>
                                    <span class="emoji-option">👭</span><span class="emoji-option">👪</span><span class="emoji-option">🤝</span><span class="emoji-option">👏</span>
                                </div>
                            </div>

                            <!-- Objects & Items -->
                            <div class="emoji-category" data-category="objects" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">📷</span><span class="emoji-option">📸</span><span class="emoji-option">🎥</span><span class="emoji-option">📹</span>
                                    <span class="emoji-option">🔊</span><span class="emoji-option">📢</span><span class="emoji-option">📣</span><span class="emoji-option">🎵</span>
                                    <span class="emoji-option">🎶</span><span class="emoji-option">🎤</span><span class="emoji-option">🪑</span><span class="emoji-option">🛋️</span>
                                    <span class="emoji-option">🍔</span><span class="emoji-option">🍕</span><span class="emoji-option">☕</span><span class="emoji-option">🥤</span>
                                    <span class="emoji-option">🏢</span><span class="emoji-option">🏪</span><span class="emoji-option">🛒</span><span class="emoji-option">💰</span>
                                </div>
                            </div>

                            <!-- Symbols & Misc -->
                            <div class="emoji-category" data-category="symbols" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">📊</span><span class="emoji-option">📈</span><span class="emoji-option">📉</span><span class="emoji-option">📋</span>
                                    <span class="emoji-option">📝</span><span class="emoji-option">📄</span><span class="emoji-option">📑</span><span class="emoji-option">🗂️</span>
                                    <span class="emoji-option">📁</span><span class="emoji-option">📂</span><span class="emoji-option">🗃️</span><span class="emoji-option">🗄️</span>
                                    <span class="emoji-option">📅</span><span class="emoji-option">📆</span><span class="emoji-option">🗓️</span><span class="emoji-option">📇</span>
                                    <span class="emoji-option">🎲</span><span class="emoji-option">🎯</span><span class="emoji-option">🎪</span><span class="emoji-option">🎨</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="add_is_active" name="is_active" checked>
                            <label class="form-check-label" for="add_is_active">
                                Active (visible to users)
                            </label>
                        </div>
                    </div>
                    
                    <!-- Preview -->
                    <div class="mb-3">
                        <label class="form-label">Preview:</label>
                        <div id="categoryPreview">
                            <span class="badge bg-primary fs-6">🔧 Engine Bay</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Add Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCategoryForm">
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCsrfToken(); ?>">
                    <input type="hidden" id="edit_category_id" name="category_id">
                    
                    <div class="mb-3">
                        <label for="edit_category_key" class="form-label">Category Key</label>
                        <input type="text" class="form-control" id="edit_category_key" name="category_key" readonly>
                        <div class="form-text">Category key cannot be changed to maintain data integrity</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_category_label" class="form-label">Category Label <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_category_label" name="category_label" 
                               maxlength="100" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_emoji_icon" class="form-label">Emoji Icon <span class="text-danger">*</span></label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="edit_emoji_icon" name="emoji_icon" 
                                   maxlength="10" required>
                            <button type="button" class="btn btn-outline-secondary" id="editEmojiPickerBtn">
                                <i class="fas fa-smile"></i> Pick
                            </button>
                        </div>
                    </div>
                    
                    <!-- Edit Comprehensive Emoji Picker -->
                    <div id="editEmojiPicker" class="mb-3" style="display: none;">
                        <div class="border rounded p-3 bg-light">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6 class="mb-0">Choose an Emoji:</h6>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary edit-emoji-category-btn active" data-category="cars">Cars</button>
                                    <button type="button" class="btn btn-outline-secondary edit-emoji-category-btn" data-category="tools">Tools</button>
                                    <button type="button" class="btn btn-outline-secondary edit-emoji-category-btn" data-category="awards">Awards</button>
                                    <button type="button" class="btn btn-outline-secondary edit-emoji-category-btn" data-category="people">People</button>
                                    <button type="button" class="btn btn-outline-secondary edit-emoji-category-btn" data-category="objects">Objects</button>
                                    <button type="button" class="btn btn-outline-secondary edit-emoji-category-btn" data-category="symbols">Symbols</button>
                                </div>
                            </div>

                            <!-- Car Related Emojis -->
                            <div class="edit-emoji-category" data-category="cars">
                                <div class="emoji-grid">
                                    <span class="emoji-option">🚗</span><span class="emoji-option">🏎️</span><span class="emoji-option">🚙</span><span class="emoji-option">🛻</span>
                                    <span class="emoji-option">🏍️</span><span class="emoji-option">🔧</span><span class="emoji-option">⚙️</span><span class="emoji-option">🛠️</span>
                                    <span class="emoji-option">🏆</span><span class="emoji-option">🥇</span><span class="emoji-option">🏅</span><span class="emoji-option">👑</span>
                                    <span class="emoji-option">🔥</span><span class="emoji-option">💨</span><span class="emoji-option">⚡</span><span class="emoji-option">🎯</span>
                                    <span class="emoji-option">📊</span><span class="emoji-option">🔊</span><span class="emoji-option">🎨</span><span class="emoji-option">✨</span>
                                    <span class="emoji-option">🪑</span><span class="emoji-option">👥</span><span class="emoji-option">📷</span><span class="emoji-option">🎪</span>
                                    <span class="emoji-option">🍔</span><span class="emoji-option">☕</span><span class="emoji-option">🏢</span><span class="emoji-option">🛒</span>
                                </div>
                            </div>

                            <!-- Copy all other categories from add modal -->
                            <div class="edit-emoji-category" data-category="tools" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">🔧</span><span class="emoji-option">🔨</span><span class="emoji-option">⚙️</span><span class="emoji-option">🛠️</span>
                                    <span class="emoji-option">🪛</span><span class="emoji-option">🔩</span><span class="emoji-option">⚡</span><span class="emoji-option">🔋</span>
                                    <span class="emoji-option">🪫</span><span class="emoji-option">💡</span><span class="emoji-option">🔌</span><span class="emoji-option">💨</span>
                                    <span class="emoji-option">🔥</span><span class="emoji-option">💧</span><span class="emoji-option">🛢️</span><span class="emoji-option">⛽</span>
                                </div>
                            </div>

                            <div class="edit-emoji-category" data-category="awards" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">🏆</span><span class="emoji-option">🥇</span><span class="emoji-option">🥈</span><span class="emoji-option">🥉</span>
                                    <span class="emoji-option">🏅</span><span class="emoji-option">🎖️</span><span class="emoji-option">👑</span><span class="emoji-option">⭐</span>
                                    <span class="emoji-option">🌟</span><span class="emoji-option">✨</span><span class="emoji-option">💎</span><span class="emoji-option">🎯</span>
                                    <span class="emoji-option">🎪</span><span class="emoji-option">🎭</span><span class="emoji-option">🎨</span><span class="emoji-option">🎬</span>
                                </div>
                            </div>

                            <div class="edit-emoji-category" data-category="people" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">👥</span><span class="emoji-option">👤</span><span class="emoji-option">👨‍⚖️</span><span class="emoji-option">👨‍🔧</span>
                                    <span class="emoji-option">👩‍🔧</span><span class="emoji-option">👨‍💼</span><span class="emoji-option">👩‍💼</span><span class="emoji-option">👨‍🏭</span>
                                    <span class="emoji-option">👩‍🏭</span><span class="emoji-option">🧑‍🤝‍🧑</span><span class="emoji-option">👫</span><span class="emoji-option">👬</span>
                                    <span class="emoji-option">👭</span><span class="emoji-option">👪</span><span class="emoji-option">🤝</span><span class="emoji-option">👏</span>
                                </div>
                            </div>

                            <div class="edit-emoji-category" data-category="objects" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">📷</span><span class="emoji-option">📸</span><span class="emoji-option">🎥</span><span class="emoji-option">📹</span>
                                    <span class="emoji-option">🔊</span><span class="emoji-option">📢</span><span class="emoji-option">📣</span><span class="emoji-option">🎵</span>
                                    <span class="emoji-option">🎶</span><span class="emoji-option">🎤</span><span class="emoji-option">🪑</span><span class="emoji-option">🛋️</span>
                                    <span class="emoji-option">🍔</span><span class="emoji-option">🍕</span><span class="emoji-option">☕</span><span class="emoji-option">🥤</span>
                                    <span class="emoji-option">🏢</span><span class="emoji-option">🏪</span><span class="emoji-option">🛒</span><span class="emoji-option">💰</span>
                                </div>
                            </div>

                            <div class="edit-emoji-category" data-category="symbols" style="display: none;">
                                <div class="emoji-grid">
                                    <span class="emoji-option">📊</span><span class="emoji-option">📈</span><span class="emoji-option">📉</span><span class="emoji-option">📋</span>
                                    <span class="emoji-option">📝</span><span class="emoji-option">📄</span><span class="emoji-option">📑</span><span class="emoji-option">🗂️</span>
                                    <span class="emoji-option">📁</span><span class="emoji-option">📂</span><span class="emoji-option">🗃️</span><span class="emoji-option">🗄️</span>
                                    <span class="emoji-option">📅</span><span class="emoji-option">📆</span><span class="emoji-option">🗓️</span><span class="emoji-option">📇</span>
                                    <span class="emoji-option">🎲</span><span class="emoji-option">🎯</span><span class="emoji-option">🎪</span><span class="emoji-option">🎨</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Edit Preview -->
                    <div class="mb-3">
                        <label class="form-label">Preview:</label>
                        <div id="editCategoryPreview">
                            <span class="badge bg-primary fs-6">🔧 Engine Bay</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.emoji-grid {
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    max-height: 200px;
    overflow-y: auto;
}

.emoji-option {
    font-size: 1.5rem;
    padding: 8px;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.emoji-option:hover {
    background-color: rgba(0,123,255,0.1);
}

.emoji-category, .edit-emoji-category {
    min-height: 120px;
}

.emoji-category-btn.active, .edit-emoji-category-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.sortable-list .category-item {
    transition: all 0.3s ease;
}

.sortable-list .category-item.ui-sortable-helper {
    transform: rotate(5deg);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.drag-handle:hover {
    color: #007bff !important;
}

/* Responsive emoji grid */
@media (max-width: 768px) {
    .emoji-grid {
        grid-template-columns: repeat(6, 1fr);
    }

    .btn-group .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .emoji-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}
</style>

<!-- Include jQuery UI for sortable functionality -->
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
<script src="<?php echo BASE_URL; ?>/public/js/event-photo-categories.js"></script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
