-- Migration Script: Convert to Advanced Custom Categories System
-- Run this SQL script in your database to upgrade to the new custom categories system

-- Step 1: Create the custom categories table
CREATE TABLE IF NOT EXISTS event_photo_custom_categories (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    category_key VARCHAR(50) NOT NULL UNIQUE,
    category_label VARCHAR(100) NOT NULL,
    emoji_icon VARCHAR(10) NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order),
    INDEX idx_category_key (category_key)
);

-- Step 2: Insert default categories with enhanced car event categories
INSERT INTO event_photo_custom_categories (category_key, category_label, emoji_icon, sort_order, is_active) VALUES
('vehicle', 'Vehicle', '🚗', 1, TRUE),
('atmosphere', 'Atmosphere', '🎪', 2, TRUE),
('awards', 'Awards', '🏆', 3, TRUE),
('vendors', 'Vendors', '🍔', 4, TRUE),
('people', 'People', '👥', 5, TRUE),
('engine', 'Engine Bay', '🔧', 6, TRUE),
('wheels', 'Wheels & Tires', '⚙️', 7, TRUE),
('interior', 'Interior', '🪑', 8, TRUE),
('exterior', 'Exterior', '✨', 9, TRUE),
('paint', 'Paint & Graphics', '🎨', 10, TRUE),
('suspension', 'Suspension', '🏁', 11, TRUE),
('exhaust', 'Exhaust', '💨', 12, TRUE),
('sound_system', 'Sound System', '🔊', 13, TRUE),
('racing', 'Racing Action', '🏎️', 14, TRUE),
('burnout', 'Burnouts', '🔥', 15, TRUE),
('dyno', 'Dyno Runs', '📊', 16, TRUE),
('judging', 'Judging', '👨‍⚖️', 17, TRUE),
('setup', 'Setup/Prep', '🛠️', 18, TRUE),
('crowd', 'Crowd Shots', '👥', 19, TRUE),
('sponsors', 'Sponsors', '🏢', 20, TRUE)
ON DUPLICATE KEY UPDATE 
    category_label = VALUES(category_label),
    emoji_icon = VALUES(emoji_icon),
    sort_order = VALUES(sort_order);

-- Step 3: Modify the event_photo_metadata table to use VARCHAR instead of ENUM
-- First, check if the table exists and has the ENUM column
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = DATABASE() AND table_name = 'event_photo_metadata');

-- If table exists, modify the column
SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE event_photo_metadata MODIFY COLUMN category VARCHAR(50) NOT NULL DEFAULT "atmosphere"',
    'SELECT "Table event_photo_metadata does not exist yet" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 4: Add foreign key constraint if table exists
SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE event_photo_metadata ADD CONSTRAINT fk_category_key 
     FOREIGN KEY (category) REFERENCES event_photo_custom_categories(category_key) ON UPDATE CASCADE',
    'SELECT "Skipping foreign key constraint" as message'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Step 5: Update any existing photos to use valid category keys
-- This ensures existing data remains compatible
UPDATE event_photo_metadata 
SET category = 'atmosphere' 
WHERE category NOT IN (SELECT category_key FROM event_photo_custom_categories)
AND EXISTS (SELECT 1 FROM information_schema.tables 
           WHERE table_schema = DATABASE() AND table_name = 'event_photo_metadata');

-- Verification queries (run these to check the migration)
-- SELECT 'Custom Categories Created:' as status;
-- SELECT category_key, category_label, emoji_icon, is_active FROM event_photo_custom_categories ORDER BY sort_order;

-- SELECT 'Event Photo Metadata Table Structure:' as status;
-- DESCRIBE event_photo_metadata;

-- SELECT 'Sample Category Usage:' as status;
-- SELECT category, COUNT(*) as count FROM event_photo_metadata GROUP BY category;

-- Migration complete!
-- You can now use the advanced custom categories system with unlimited categories.
