/**
 * Event Photo Categories Management
 * Advanced custom category system with drag & drop, emoji picker, and AJAX operations
 */

class EventPhotoCategoriesManager {
    constructor() {
        this.baseUrl = window.location.origin;
        this.csrfToken = document.querySelector('input[name="csrf_token"]')?.value;
        this.init();
    }

    init() {
        this.initSortable();
        this.initEventListeners();
        this.initEmojiPickers();
        this.initPreviewUpdates();
    }

    initSortable() {
        if (typeof $ !== 'undefined' && $.fn.sortable) {
            $('#categoriesList').sortable({
                handle: '.drag-handle',
                placeholder: 'sortable-placeholder',
                helper: 'clone',
                tolerance: 'pointer',
                update: (event, ui) => {
                    this.updateSortOrder();
                }
            });
        }
    }

    initEventListeners() {
        // Add category form
        document.getElementById('addCategoryForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addCategory();
        });

        // Edit category form
        document.getElementById('editCategoryForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.editCategory();
        });

        // Edit category buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.edit-category')) {
                const btn = e.target.closest('.edit-category');
                this.showEditModal(btn);
            }
            
            if (e.target.closest('.delete-category')) {
                const btn = e.target.closest('.delete-category');
                this.deleteCategory(btn);
            }
            
            if (e.target.closest('.toggle-status')) {
                const btn = e.target.closest('.toggle-status');
                this.toggleCategoryStatus(btn);
            }
        });

        // Bulk actions
        document.getElementById('selectAll')?.addEventListener('click', () => {
            document.querySelectorAll('.category-checkbox').forEach(cb => cb.checked = true);
        });

        document.getElementById('deselectAll')?.addEventListener('click', () => {
            document.querySelectorAll('.category-checkbox').forEach(cb => cb.checked = false);
        });

        document.getElementById('enableSelected')?.addEventListener('click', () => {
            this.bulkToggleStatus(true);
        });

        document.getElementById('disableSelected')?.addEventListener('click', () => {
            this.bulkToggleStatus(false);
        });
    }

    initEmojiPickers() {
        // Add modal emoji picker
        document.getElementById('emojiPickerBtn')?.addEventListener('click', () => {
            const picker = document.getElementById('emojiPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        });

        // Edit modal emoji picker
        document.getElementById('editEmojiPickerBtn')?.addEventListener('click', () => {
            const picker = document.getElementById('editEmojiPicker');
            picker.style.display = picker.style.display === 'none' ? 'block' : 'none';
        });

        // Emoji selection for add modal
        document.querySelectorAll('#emojiPicker .emoji-option').forEach(emoji => {
            emoji.addEventListener('click', () => {
                document.getElementById('add_emoji_icon').value = emoji.textContent;
                document.getElementById('emojiPicker').style.display = 'none';
                this.updateAddPreview();
            });
        });

        // Emoji selection for edit modal
        document.querySelectorAll('#editEmojiPicker .emoji-option').forEach(emoji => {
            emoji.addEventListener('click', () => {
                document.getElementById('edit_emoji_icon').value = emoji.textContent;
                document.getElementById('editEmojiPicker').style.display = 'none';
                this.updateEditPreview();
            });
        });
    }

    initPreviewUpdates() {
        // Add modal preview updates
        ['add_emoji_icon', 'add_category_label'].forEach(id => {
            document.getElementById(id)?.addEventListener('input', () => {
                this.updateAddPreview();
            });
        });

        // Edit modal preview updates
        ['edit_emoji_icon', 'edit_category_label'].forEach(id => {
            document.getElementById(id)?.addEventListener('input', () => {
                this.updateEditPreview();
            });
        });

        // Category key validation
        document.getElementById('add_category_key')?.addEventListener('input', (e) => {
            e.target.value = e.target.value.toLowerCase().replace(/[^a-z0-9_]/g, '');
        });
    }

    updateAddPreview() {
        const emoji = document.getElementById('add_emoji_icon')?.value || '🔧';
        const label = document.getElementById('add_category_label')?.value || 'Category Name';
        const preview = document.getElementById('categoryPreview');
        if (preview) {
            preview.innerHTML = `<span class="badge bg-primary fs-6">${emoji} ${label}</span>`;
        }
    }

    updateEditPreview() {
        const emoji = document.getElementById('edit_emoji_icon')?.value || '🔧';
        const label = document.getElementById('edit_category_label')?.value || 'Category Name';
        const preview = document.getElementById('editCategoryPreview');
        if (preview) {
            preview.innerHTML = `<span class="badge bg-primary fs-6">${emoji} ${label}</span>`;
        }
    }

    async addCategory() {
        const formData = new FormData(document.getElementById('addCategoryForm'));
        
        try {
            const response = await fetch(`${this.baseUrl}/admin/add_event_photo_category`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Category added successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(result.message || 'Failed to add category', 'danger');
            }
        } catch (error) {
            console.error('Add category error:', error);
            this.showAlert('An error occurred while adding the category', 'danger');
        }
    }

    showEditModal(btn) {
        const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
        
        document.getElementById('edit_category_id').value = btn.dataset.id;
        document.getElementById('edit_category_key').value = btn.dataset.key;
        document.getElementById('edit_category_label').value = btn.dataset.label;
        document.getElementById('edit_emoji_icon').value = btn.dataset.emoji;
        
        this.updateEditPreview();
        modal.show();
    }

    async editCategory() {
        const formData = new FormData(document.getElementById('editCategoryForm'));
        
        try {
            const response = await fetch(`${this.baseUrl}/admin/edit_event_photo_category`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Category updated successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(result.message || 'Failed to update category', 'danger');
            }
        } catch (error) {
            console.error('Edit category error:', error);
            this.showAlert('An error occurred while updating the category', 'danger');
        }
    }

    async deleteCategory(btn) {
        const categoryLabel = btn.dataset.label;
        const categoryId = btn.dataset.id;
        
        if (!confirm(`Are you sure you want to delete the "${categoryLabel}" category? This action cannot be undone.`)) {
            return;
        }

        try {
            const formData = new FormData();
            formData.append('category_id', categoryId);
            formData.append('csrf_token', this.csrfToken);

            const response = await fetch(`${this.baseUrl}/admin/delete_event_photo_category`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Category deleted successfully!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(result.message || 'Failed to delete category', 'danger');
            }
        } catch (error) {
            console.error('Delete category error:', error);
            this.showAlert('An error occurred while deleting the category', 'danger');
        }
    }

    async toggleCategoryStatus(btn) {
        const categoryId = btn.dataset.id;
        
        try {
            const formData = new FormData();
            formData.append('category_id', categoryId);
            formData.append('csrf_token', this.csrfToken);

            const response = await fetch(`${this.baseUrl}/admin/toggle_event_photo_category_status`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Category status updated!', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(result.message || 'Failed to update category status', 'danger');
            }
        } catch (error) {
            console.error('Toggle status error:', error);
            this.showAlert('An error occurred while updating the category status', 'danger');
        }
    }

    async bulkToggleStatus(enable) {
        const selectedIds = Array.from(document.querySelectorAll('.category-checkbox:checked'))
            .map(cb => cb.value);
        
        if (selectedIds.length === 0) {
            this.showAlert('Please select at least one category', 'warning');
            return;
        }

        try {
            const formData = new FormData();
            formData.append('category_ids', JSON.stringify(selectedIds));
            formData.append('enable', enable ? '1' : '0');
            formData.append('csrf_token', this.csrfToken);

            const response = await fetch(`${this.baseUrl}/admin/bulk_toggle_event_photo_categories`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert(`Categories ${enable ? 'enabled' : 'disabled'} successfully!`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                this.showAlert(result.message || 'Failed to update categories', 'danger');
            }
        } catch (error) {
            console.error('Bulk toggle error:', error);
            this.showAlert('An error occurred while updating the categories', 'danger');
        }
    }

    async updateSortOrder() {
        const sortOrder = Array.from(document.querySelectorAll('.category-item'))
            .map((item, index) => ({
                id: item.dataset.id,
                sort_order: index + 1
            }));

        try {
            const formData = new FormData();
            formData.append('sort_order', JSON.stringify(sortOrder));
            formData.append('csrf_token', this.csrfToken);

            const response = await fetch(`${this.baseUrl}/admin/update_event_photo_category_order`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            
            if (result.success) {
                this.showAlert('Category order updated!', 'success', 2000);
            } else {
                this.showAlert('Failed to update category order', 'danger');
            }
        } catch (error) {
            console.error('Update sort order error:', error);
            this.showAlert('An error occurred while updating the category order', 'danger');
        }
    }

    showAlert(message, type, duration = 5000) {
        // Remove existing alerts
        document.querySelectorAll('.dynamic-alert').forEach(alert => alert.remove());
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show dynamic-alert`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild.nextSibling);
        
        // Auto-dismiss after duration
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, duration);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new EventPhotoCategoriesManager();
});
